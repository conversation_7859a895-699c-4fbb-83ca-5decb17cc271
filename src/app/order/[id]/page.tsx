'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { Calendar, Clock, DollarSign, User, FileText, CheckCircle, AlertCircle, XCircle, CreditCard } from 'lucide-react'
import { orderService } from '@/lib/database'
import type { Order } from '@/lib/supabase'

const OrderTrackingPage = () => {
  const params = useParams()
  const orderId = params.id as string
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Mock order data for demonstration
  const mockOrder: Order = {
    id: orderId,
    customer_name: '<PERSON>',
    customer_email: '<EMAIL>',
    service_name: 'E-commerce Website Development',
    description: 'Custom e-commerce platform with payment integration, product catalog, and admin dashboard. Includes responsive design, SEO optimization, and mobile app integration.',
    deadline: '2024-02-15',
    price: 2500.00,
    payment_status: 'pending',
    progress_status: 'in_progress',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z'
  }

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        // In a real application, fetch from database
        // const orderData = await orderService.getById(orderId)
        
        // Simulate API call
        setTimeout(() => {
          if (orderId) {
            setOrder(mockOrder)
          } else {
            setError('Order not found')
          }
          setLoading(false)
        }, 1000)
      } catch (err) {
        setError('Failed to load order details')
        setLoading(false)
      }
    }

    fetchOrder()
  }, [orderId])

  const getProgressSteps = (status: string) => {
    const steps = [
      { id: 'not_started', label: 'Order Received', icon: FileText },
      { id: 'in_progress', label: 'In Development', icon: Clock },
      { id: 'completed', label: 'Completed', icon: CheckCircle }
    ]

    const currentIndex = steps.findIndex(step => step.id === status)
    
    return steps.map((step, index) => ({
      ...step,
      status: index <= currentIndex ? 'completed' : 'pending'
    }))
  }

  const getPaymentStatusInfo = (status: string) => {
    switch (status) {
      case 'paid':
        return {
          icon: CheckCircle,
          text: 'Payment Completed',
          color: 'text-green-600',
          bgColor: 'bg-green-100'
        }
      case 'pending':
        return {
          icon: AlertCircle,
          text: 'Payment Pending',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100'
        }
      default:
        return {
          icon: XCircle,
          text: 'Payment Failed',
          color: 'text-red-600',
          bgColor: 'bg-red-100'
        }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading order details...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Order Not Found</h1>
            <p className="text-gray-600 mb-8">{error || 'The order you are looking for does not exist.'}</p>
            <a
              href="/"
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
            >
              Go to Homepage
            </a>
          </div>
        </div>
      </div>
    )
  }

  const progressSteps = getProgressSteps(order.progress_status)
  const paymentInfo = getPaymentStatusInfo(order.payment_status)
  const PaymentIcon = paymentInfo.icon

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Order Tracking
          </h1>
          <p className="text-lg text-gray-600">
            Order ID: <span className="font-mono font-semibold">{order.id}</span>
          </p>
        </motion.div>

        {/* Order Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-white rounded-2xl p-8 shadow-lg mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Project Details</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <FileText className="w-5 h-5 text-gray-400 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">{order.service_name}</p>
                    <p className="text-gray-600 text-sm mt-1">{order.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-semibold text-gray-900">{order.customer_name}</p>
                    <p className="text-gray-600 text-sm">{order.customer_email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-semibold text-gray-900">Deadline</p>
                    <p className="text-gray-600 text-sm">
                      {new Date(order.deadline).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Payment Information</h2>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <DollarSign className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-semibold text-gray-900">Total Amount</p>
                    <p className="text-2xl font-bold text-primary-600">${order.price.toFixed(2)}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <PaymentIcon className={`w-5 h-5 ${paymentInfo.color}`} />
                  <div>
                    <p className="font-semibold text-gray-900">Payment Status</p>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${paymentInfo.bgColor} ${paymentInfo.color}`}>
                      {paymentInfo.text}
                    </span>
                  </div>
                </div>

                {order.payment_status === 'pending' && (
                  <div className="mt-6">
                    <button className="w-full bg-secondary-500 hover:bg-secondary-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2">
                      <CreditCard size={20} />
                      <span>Complete Payment</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Progress Tracking */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl p-8 shadow-lg mb-8"
        >
          <h2 className="text-xl font-bold text-gray-900 mb-8">Project Progress</h2>
          
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute left-6 top-6 bottom-6 w-0.5 bg-gray-200"></div>
            <div 
              className="absolute left-6 top-6 w-0.5 bg-primary-500 transition-all duration-1000"
              style={{ 
                height: `${(progressSteps.filter(step => step.status === 'completed').length - 1) * 100 / (progressSteps.length - 1)}%` 
              }}
            ></div>

            {/* Progress Steps */}
            <div className="space-y-8">
              {progressSteps.map((step, index) => {
                const Icon = step.icon
                return (
                  <motion.div
                    key={step.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                    className="relative flex items-center"
                  >
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center relative z-10 ${
                      step.status === 'completed' 
                        ? 'bg-primary-500 text-white' 
                        : 'bg-gray-200 text-gray-400'
                    }`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <div className="ml-6">
                      <h3 className={`font-semibold ${
                        step.status === 'completed' ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {step.label}
                      </h3>
                      {step.status === 'completed' && (
                        <p className="text-sm text-gray-600 mt-1">
                          {step.id === 'not_started' && 'Order received and confirmed'}
                          {step.id === 'in_progress' && 'Development is currently in progress'}
                          {step.id === 'completed' && 'Project completed and delivered'}
                        </p>
                      )}
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </motion.div>

        {/* Contact Support */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 text-white text-center"
        >
          <h2 className="text-2xl font-bold mb-4">Need Help?</h2>
          <p className="text-primary-100 mb-6">
            Have questions about your order? Our team is here to help you 24/7.
          </p>
          <a
            href={`https://wa.me/${process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '914858483'}?text=Hi, I have a question about my order ${order.id}`}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-white text-primary-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-colors duration-200 inline-block"
          >
            Contact Support
          </a>
        </motion.div>
      </div>
    </div>
  )
}

export default OrderTrackingPage
