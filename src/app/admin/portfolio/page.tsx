'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Plus, Edit, Trash2, ExternalLink, Eye } from 'lucide-react'
import { portfolioService } from '@/lib/database'
import type { PortfolioItem } from '@/lib/supabase'

const AdminPortfolioPage = () => {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)

  // Mock data for demonstration
  const mockPortfolioItems: PortfolioItem[] = [
    {
      id: '1',
      title: 'E-commerce Platform',
      description: 'A modern e-commerce platform built with Next.js and Stripe integration.',
      image_url: '/images/portfolio/ecommerce.jpg',
      project_url: 'https://demo-ecommerce.com',
      technologies: ['Next.js', 'React', 'TypeScript', 'Stripe', 'PostgreSQL'],
      created_at: '2024-01-15T00:00:00Z'
    },
    {
      id: '2',
      title: 'WhatsApp Business Bot',
      description: 'Intelligent customer service bot for WhatsApp Business API.',
      image_url: '/images/portfolio/whatsapp-bot.jpg',
      technologies: ['Node.js', 'WhatsApp API', 'OpenAI', 'MongoDB'],
      created_at: '2024-02-10T00:00:00Z'
    },
    {
      id: '3',
      title: 'Inventory Management System',
      description: 'Complete inventory tracking and management solution for retail businesses.',
      image_url: '/images/portfolio/inventory.jpg',
      project_url: 'https://demo-inventory.com',
      technologies: ['React', 'Express.js', 'MongoDB', 'Chart.js'],
      created_at: '2024-03-05T00:00:00Z'
    }
  ]

  useEffect(() => {
    // Simulate loading from database
    setTimeout(() => {
      setPortfolioItems(mockPortfolioItems)
      setLoading(false)
    }, 1000)
  }, [])

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this portfolio item?')) {
      try {
        // await portfolioService.delete(id)
        setPortfolioItems(items => items.filter(item => item.id !== id))
      } catch (error) {
        console.error('Error deleting portfolio item:', error)
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Portfolio Management</h1>
          <p className="text-gray-600 mt-1">Manage your portfolio items and showcase your work</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors duration-200"
        >
          <Plus size={20} />
          <span>Add New Item</span>
        </button>
      </div>

      {/* Portfolio Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {portfolioItems.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
          >
            {/* Project Image */}
            <div className="h-48 bg-gradient-to-br from-primary-100 to-secondary-100 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4 mx-auto">
                    <span className="text-2xl font-bold text-primary-600">
                      {item.title.charAt(0)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">{item.title}</p>
                </div>
              </div>
            </div>

            {/* Project Content */}
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {item.title}
              </h3>
              
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                {item.description}
              </p>

              {/* Technologies */}
              <div className="flex flex-wrap gap-2 mb-4">
                {item.technologies.slice(0, 3).map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {tech}
                  </span>
                ))}
                {item.technologies.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                    +{item.technologies.length - 3}
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200">
                    <Eye size={16} />
                  </button>
                  <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200">
                    <Edit size={16} />
                  </button>
                  <button 
                    onClick={() => handleDelete(item.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                
                {item.project_url && (
                  <a
                    href={item.project_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                  >
                    <ExternalLink size={16} />
                  </a>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {portfolioItems.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Plus className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No Portfolio Items</h3>
          <p className="text-gray-600 mb-6">Get started by adding your first portfolio item.</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
          >
            Add Portfolio Item
          </button>
        </motion.div>
      )}

      {/* Add Modal Placeholder */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Add Portfolio Item</h2>
            <p className="text-gray-600 mb-6">
              Portfolio item creation form would go here. This is a placeholder for the full implementation.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminPortfolioPage
