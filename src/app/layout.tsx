import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { AuthProvider } from "@/contexts/AuthContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Apka Ideas - Professional Website, Bot & Automation Services",
  description: "Transform your business with custom websites, intelligent bots, and powerful automation tools. Professional development services for modern businesses.",
  keywords: "website development, bot development, automation tools, web design, custom software",
  authors: [{ name: "Apka Ideas" }],
  openGraph: {
    title: "Apka Ideas - Professional Development Services",
    description: "Transform your business with custom websites, intelligent bots, and powerful automation tools.",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased bg-white text-gray-900">
        <AuthProvider>
          <div className="min-h-screen flex flex-col">
            <Navbar />
            <main className="flex-grow">
              {children}
            </main>
            <Footer />
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
