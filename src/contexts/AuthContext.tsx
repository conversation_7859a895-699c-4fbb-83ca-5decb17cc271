'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
// import { adminService } from '@/lib/database'

interface AuthContextType {
  isAdmin: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  loading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in (demo implementation)
    const adminToken = localStorage.getItem('admin_token')
    if (adminToken === 'demo_admin_token') {
      setIsAdmin(true)
    }
    setLoading(false)
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    // Demo login - in production, this would validate against your backend
    if (email === '<EMAIL>' && password === 'admin123') {
      setIsAdmin(true)
      localStorage.setItem('admin_token', 'demo_admin_token')
      return true
    }
    return false
  }

  const logout = () => {
    setIsAdmin(false)
    localStorage.removeItem('admin_token')
  }

  const value = {
    isAdmin,
    login,
    logout,
    loading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
