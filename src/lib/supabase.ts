// Demo mode - Supabase client disabled for demonstration
// import { createClient } from '@supabase/supabase-js'

// const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
// const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface PortfolioItem {
  id: string
  title: string
  description: string
  image_url: string
  project_url?: string
  technologies: string[]
  created_at: string
}

export interface Order {
  id: string
  customer_name: string
  customer_email: string
  service_name: string
  description: string
  deadline: string
  price: number
  payment_status: 'pending' | 'paid'
  progress_status: 'not_started' | 'in_progress' | 'completed'
  created_at: string
  updated_at: string
}

export interface ContactSubmission {
  id: string
  name: string
  email: string
  message: string
  created_at: string
}
